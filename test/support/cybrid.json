{"openapi": "3.0.3", "info": {"title": "Cybrid Bank API", "termsOfService": "https://www.cybrid.xyz/tos", "description": "# Cybrid API documentation\n\nWelcome to Cybrid, an all-in-one crypto platform that enables you to easily **build** and **launch** white-label crypto products or services.\n\nIn these documents, you'll find details on how our REST API operates and generally how our platform functions.\n\nIf you're looking for our UI SDK Widgets for Web or Mobile (iOS/Android), generated API clients, or demo applications, head over to our [Github repo](https://github.com/Cybrid-app).\n\n💡 We recommend bookmarking the [Cybrid LinkTree](https://linktr.ee/cybridtechnologies) which contains many helpful links to platform resources.\n\n## Getting Started\n\nThis is Cybrid's public interactive API documentation, which allows you to fully test our APIs. If you'd like to use a different tool to exercise our APIs, you can download the [Open API 3.0 yaml](https://bank.sandbox.cybrid.app/api/schema/v1/swagger.yaml) for import.\n\nIf you're new to our APIs and the Cybrid Platform, follow the below guides to get set up and familiar with the platform:\n\n1. [Understanding the Platform](https://kb.cybrid.xyz/understanding-the-platform)\n2. [Getting Started in the Cybrid Sandbox](https://kb.cybrid.xyz/getting-started-guide)\n3. [Getting Ready for Trading](https://kb.cybrid.xyz/getting-ready-for-trading)\n4. [Running the Web Demo App](https://kb.cybrid.xyz/locally-running-the-web-demo-app) (or, alternatively, [Testing with Hosted Web Demo App](https://kb.cybrid.xyz/testing-with-hosted-web-demo-app))\n\nIn [Getting Started in the Cybrid Sandbox](https://kb.cybrid.xyz/getting-started-guide), we walk you through how to use the [Cybrid Sandbox](https://id.sandbox.cybrid.app/) to create a test bank and generate API keys. In [Getting Ready for Trading](https://kb.cybrid.xyz/getting-ready-for-trading), we walk through creating customers, customer identities, accounts, as well as executing quotes and trades.\n\nIf you've already run through the first two guides, you can follow the [Running the Web Demo App](https://kb.cybrid.xyz/locally-running-the-web-demo-app) guide to test our web SDK with your sandbox `bank` and `customer`.\n\n## Working with the Cybrid Platform\n\nThere are three primary ways you can interact with the Cybrid platform:\n\n1. Directly via our RESTful API (this documentation)\n2. Using our API clients available in a variety of languages ([Angular](https://github.com/Cybrid-app/cybrid-api-bank-angular), [Java](https://github.com/Cybrid-app/cybrid-api-bank-java), [Kotlin](https://github.com/Cybrid-app/cybrid-api-bank-kotlin), [Python](https://github.com/Cybrid-app/cybrid-api-bank-python), [Ruby](https://github.com/Cybrid-app/cybrid-api-bank-ruby), [Swift](https://github.com/Cybrid-app/cybrid-api-bank-swift) or [Typescript](https://github.com/Cybrid-app/cybrid-api-bank-typescript))\n3. Integrating a platform specific SDK ([Web](https://github.com/Cybrid-app/cybrid-sdk-web), [Android](https://github.com/Cybrid-app/cybrid-sdk-android), [iOS](https://github.com/Cybrid-app/cybrid-sdk-ios))\n\nOur complete set of APIs allows you to manage resources across three distinct areas: your `Organization`, your `Banks` and your `Identities`. For most of your testing and interaction you'll be using the `Bank` API, which is where the majority of APIs reside.\n\n*The complete set of APIs can be found on the following pages:*\n\n| API                                                              | Description                                                 |\n|------------------------------------------------------------------|-------------------------------------------------------------|\n| [Organization API](https://organization.sandbox.cybrid.app/api/schema/swagger-ui)   | APIs to manage organizations                                |\n| [Bank API](https://bank.sandbox.cybrid.app/api/schema/swagger-ui)                   | APIs to manage banks (and all downstream customer activity) |\n| [Identities API](https://id.sandbox.cybrid.app/api/schema/swagger-ui)                       | APIs to manage organization and bank identities             |\n\nFor questions please contact [Support](mailto:<EMAIL>) at any time for assistance, or contact the [Product Team](mailto:<EMAIL>) for product suggestions.\n\n## Authenticating with the API\n\nThe Cybrid Platform uses OAuth 2.0 Bearer Tokens to authenticate requests to the platform. Credentials to create `Organization` and `Bank` tokens can be generated via the [Cybrid Sandbox](https://id.sandbox.cybrid.app). Access tokens can be generated for a `Customer` as well via the [Cybrid IdP](https://id.sandbox.cybrid.app) as well.\n\nAn `Organization` access token applies broadly to the whole Organization and all of its `Banks`, whereas, a `Bank` access token is specific to an individual Bank. `Customer` tokens, similarly, are scoped to a specific customer in a bank.\n\nBoth `Organization` and `Bank` tokens can be created using the OAuth Client Credential Grant flow. Each Organization and Bank has its own unique `Client ID` and `Secret` that allows for machine-to-machine authentication.\n\nA `Bank` can then generate `Customer` access tokens via API using our [Identities API](https://id.sandbox.cybrid.app/api/schema/swagger-ui).\n\n<font color=\"orange\">**âš ï¸ Never share your Client ID or Secret publicly or in your source code repository.**</font>\n\nYour `Client ID` and `Secret` can be exchanged for a time-limited `Bearer Token` by interacting with the Cybrid Identity Provider or through interacting with the **Authorize** button in this document.\n\nThe following curl command can be used to quickly generate a `Bearer Token` for use in testing the API or demo applications.\n\n```\n# Example request when using Bank credentials\ncurl -X POST https://id.sandbox.cybrid.app/oauth/token -d '{\n    \"grant_type\": \"client_credentials\",\n    \"client_id\": \"<Your Client ID>\",\n    \"client_secret\": \"<Your Secret>\",\n    \"scope\": \"banks:read banks:write accounts:read accounts:execute customers:read customers:write customers:execute prices:read quotes:execute quotes:read trades:execute trades:read transfers:execute transfers:read rewards:execute rewards:read external_bank_accounts:read external_bank_accounts:write external_bank_accounts:execute external_wallets:read external_wallets:execute workflows:read workflows:execute deposit_addresses:read deposit_addresses:execute\"\n  }' -H \"Content-Type: application/json\"\n\n# When using Organization credentials set `scope` to 'organizations:read organizations:write banks:read banks:write banks:execute customers:read accounts:read prices:read quotes:execute quotes:read trades:execute trades:read transfers:read transfers:execute external_bank_accounts:read external_wallets:read workflows:read deposit_addresses:read'\n```\n<font color=\"orange\">**âš ï¸ Note: The above curl will create a bearer token with full scope access. Delete scopes if you'd like to restrict access.**</font>\n\n## Authentication Scopes\n\nThe Cybrid platform supports the use of scopes to control the level of access a token is limited to. Scopes do not grant access to resources; instead, they provide limits, in support of the least privilege principal.\n\nThe following scopes are available on the platform and can be requested when generating either an Organization, Bank or Customer token. Generally speaking, the _Read_ scope is required to read and list resources, the _Write_ scope is required to update a resource and the _Execute_ scope is required to create a resource.\n\n| Resource              | Read scope (Token Type)                                    | Write scope (Token Type)                      | Execute scope (Token Type)                       |\n|-----------------------|------------------------------------------------------------|-----------------------------------------------|--------------------------------------------------|\n| Account               | accounts:read (Organization, Bank, Customer)               |                                               | accounts:execute (Bank, Customer)                |\n| Bank                  | banks:read (Organization, Bank)                            | banks:write (Organization, Bank)              | banks:execute (Organization)                     |\n| Customer              | customers:read (Organization, Bank, Customer)              | customers:write (Bank, Customer)              | customers:execute (Bank)                         |\n| Deposit Address       | deposit_addresses:read (Organization, Bank, Customer)      | deposit_addresses:write (Bank, Customer)      | deposit_addresses:execute (Bank, Customer)       |\n| External Bank Account | external_bank_accounts:read (Organization, Bank, Customer) | external_bank_accounts:write (Bank, Customer) | external_bank_accounts:execute (Bank, Customer)  |\n| External Wallet       | external_wallet:read (Organization, Bank, Customer)        |                                               | external_wallet:execute (Bank, Customer)         |\n| Organization          | organizations:read (Organization)                          | organizations:write (Organization)            |                                                  |\n| Price                 | prices:read (Bank, Customer)                               |                                               |                                                  |\n| Quote                 | quotes:read (Organization, Bank, Customer)                 |                                               | quotes:execute (Organization, Bank, Customer)    |\n| Reward                | rewards:read (Bank, Customer)                              |                                               | rewards:execute (Bank)                           |\n| Trade                 | trades:read (Organization, Bank, Customer)                 |                                               | trades:execute (Organization, Bank, Customer)    |\n| Transfer              | transfers:read (Organization, Bank, Customer)              |                                               | transfers:execute (Organization, Bank, Customer) |\n| Workflow              | workflows:read (Organization, Bank, Customer)              |                                               | workflows:execute (Bank, Customer)               |\n\n## Available Endpoints\n\nThe available APIs for the [Identity](https://id.sandbox.cybrid.app/api/schema/swagger-ui), [Organization](https://organization.sandbox.cybrid.app/api/schema/swagger-ui) and [Bank](https://bank.sandbox.cybrid.app/api/schema/swagger-ui) API services are listed below:\n\n| API Service  | Model                | API Endpoint Path              | Description                                                                                       |\n|--------------|----------------------|--------------------------------|---------------------------------------------------------------------------------------------------|\n| Identity     | Bank                 | /api/bank_applications         | Create and list banks                                                                             |\n| Identity     | CustomerToken        | /api/customer_tokens           | Create customer JWT access tokens                                                                 |\n| Identity     | Organization         | /api/organization_applications | Create and list organizations                                                                     |\n| Organization | Organization         | /api/organizations             | APIs to retrieve and update organization name                                                     |\n| Bank         | Account              | /api/accounts                  | Create and list accounts, which hold a specific asset for a customers                             |\n| Bank         | Asset                | /api/assets                    | Get a list of assets supported by the platform (ex: BTC, ETH)                                     |\n| Bank         | Bank                 | /api/banks                     | Create, update and list banks, the parent to customers, accounts, etc                             |\n| Bank         | Customer             | /api/customers                 | Create and list customers                                                                         |\n| Bank         | DepositAddress       | /api/deposit_addresses         | Create, get and list deposit addresses                                                            |\n| Bank         | ExternalBankAccount  | /api/external_bank_accounts    | Create, get and list external bank accounts, which connect customer bank accounts to the platform |\n| Bank         | ExternalWallet       | /api/external_wallets          | Create, get, list and delete external wallets, which connect customer wallets to the platform     |\n| Bank         | IdentityVerification | /api/identity_verifications    | Create and list identity verifications, which are performed on customers for KYC                  |\n| Bank         | Price                | /api/prices                    | Get the current prices for assets on the platform                                                 |\n| Bank         | Quote                | /api/quotes                    | Create and list quotes, which are required to execute trades                                      |\n| Bank         | Reward               | /api/rewards                   | Create a new reward (automates quote/trade for simplicity)                                        |\n| Bank         | Symbol               | /api/symbols                   | Get a list of symbols supported for trade (ex: BTC-USD)                                           |\n| Bank         | Trade                | /api/trades                    | Create and list trades, which buy or sell cryptocurrency                                          |\n| Bank         | Transfer             | /api/transfers                 | Create, get and list transfers (e.g., funding, book)                                              |\n| Bank         | Workflow             | /api/workflows                 | Create, get and list workflows                                                                    |\n\n## Understanding Object Models & Endpoints\n\n**Organizations**\n\nAn `Organization` is meant to represent the organization partnering with Cybrid to use our platform.\n\nAn `Organization` typically does not directly interact with `customers`. Instead, an Organization has one or more `banks`, which encompass the financial service offerings of the platform.\n\n**Banks**\n\nA `Bank` is owned by an `Organization` and can be thought of as an environment or container for `customers` and product offerings. Banks are created in either `Sandbox` or `Production` mode, where `Sandbox` is the environment that you would test, prototype and build in prior to moving to `Production`.\n\nAn `Organization` can have multiple `banks`, in either `Sandbox` or `Production` environments. A `Sandbox Bank` will be backed by stubbed data and process flows. For instance, funding source transfer processes as well as trades will be simulated rather than performed, however asset prices are representative of real-world values. You have an unlimited amount of simulated fiat currency for testing purposes.\n\n**Customers**\n\n`Customers` represent your banking users on the platform. At present, we offer support for `Individuals` as Customers.\n\n`Customers` must be verified (i.e., KYC'd) in our system before they can play any part on the platform, which means they must have an associated and a passing `Identity Verification`. See the Identity Verifications section for more details on how a customer can be verified.\n\n`Customers` must also have an `Account` to be able to transact, in the desired asset class. See the Accounts APIs for more details on setting up accounts for the customer.\n", "version": "v0.74.25", "contact": {"name": "Cybrid Support", "email": "<EMAIL>"}, "license": {"name": "Apache-2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.html"}}, "paths": {"/api/assets": {"get": {"summary": "Get assets list", "tags": ["Assets"], "operationId": "listAssets", "description": "Retrieves a listing of assets.", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}], "responses": {"200": {"description": "get list of assets", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetList"}}}}}}}, "/api/banks": {"post": {"summary": "Create Bank", "tags": ["Banks"], "operationId": "createBank", "description": "Creates a bank.\n\n## Bank Type\n\nBank's can be created in either `sandbox` or `production` mode. Sandbox Banks will not transact in real fiat dollars or cryptocurrencies.\n\nVia the API, only `sandbox` banks can be created. In order to enable a `production` bank please contact [Support](mailto:<EMAIL>).\n\n## Backstopped Funding Source\n\nA Backstopped Funding Source is an agreement between the Organization and Cybrid on transaction settlement. A Backstopped Funding Source is an escrow bank account that the Organization tops up on a regular basis and is drawn down by the Cybrid Platform when trades are executed by the Bank for its customers.\n\nThis feature must specifically be enabled in the Bank's `features` array by including the `backstopped_funding_source` flag.\n\nTo allow customers to trade in a bank that has a Backstopped Funding Source the customer's `fiat` account must be topped up using a `book transfer` prior to executing any trades.\n\nPlease contact [Support](mailto:<EMAIL>) to inquire about enabling this option for your Organization.\n\n## Plaid Funding Source\n\nA Plaid Funding Source allows customers to directly connect their TradFi bank accounts to the Cybrid platform.\n\nThis feature must specifically be enabled in the Bank's `features` array by including the `plaid_funding_source` flag.\n\nTo allow customers to trade in a bank that has a Plaid Funding Source the customer's `fiat` account must first be connected to the platform using Cybrid's `Workflows` and `ExternalBankAccounts` APIs. Once their `ExternalBankAccount` is created on the Cybrid Platform their `fiat` account must be topped up using a `funding` transfer prior to executing any trades.\n\n## Attestation Identity Records\n\nAttestation Identity Records is a method the Cybrid Platform uses to verify the identity of a Bank's customers. Please see the documentation under the create Identity Record API for more technical detail.\n\nThis feature must specifically be enabled in the Bank's `features` array by including the `attestation_identity_records` flag. At present, attestation identity records is the supported method for verifying a customer's identity on the platform.\n\nPlease contact [Support](mailto:<EMAIL>) to inquire about enabling this option for your Organization.\n\n\n\nRequired scope: **banks:execute**", "security": [{"BearerAuth": [], "oauth2": ["banks:execute"]}], "parameters": [], "responses": {"201": {"description": "Bank created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Bank"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: name, type, features, supported_trading_symbols, supported_country_codes", "message_code": "missing_parameter"}}, "invalid_parameter_value": {"value": {"status": 400, "error_message": "Invalid value exception. Parameter: type supported values: [\"sandbox\"] ", "message_code": "invalid_value"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, invalid subject", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: banks:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unsupported_parameter": {"value": {"status": 415, "error_message": "Unsupported value exception. Parameter: features supported values: [ attestation_identity_records, backstopped_funding_source ]", "message_code": "unsupported_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostBank"}}}, "required": true}}, "get": {"summary": "Get banks list", "tags": ["Banks"], "operationId": "listBanks", "description": "Retrieves a listing of bank.\n\nRequired scope: **banks:read**", "security": [{"BearerAuth": [], "oauth2": ["banks:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list banks for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of banks", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankList"}}}}, "401": {"description": "Unauthorized - invalid subject, Authentication failed", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: banks:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/banks/{bank_guid}": {"get": {"summary": "Get Bank", "tags": ["Banks"], "operationId": "getBank", "description": "Retrieves a bank.\n\nRequired scope: **banks:read**", "security": [{"BearerAuth": [], "oauth2": ["banks:read"]}], "parameters": [{"name": "bank_guid", "in": "path", "required": true, "description": "Identifier for the bank.", "schema": {"type": "string"}}], "responses": {"200": {"description": "bank found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Bank"}}}}, "401": {"description": "Unauthorized - Authentication failed, invalid subject", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: banks:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "bank not found", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "invalid bank_guid value", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "patch": {"summary": "Patch Bank", "tags": ["Banks"], "operationId": "updateBank", "description": "Update a bank.\n\nRequired scope: **banks:write**", "security": [{"BearerAuth": [], "oauth2": ["banks:write"]}], "parameters": [{"name": "bank_guid", "in": "path", "required": true, "description": "Identifier for the bank.", "schema": {"type": "string"}}], "responses": {"200": {"description": "bank found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Bank"}}}}, "401": {"description": "Unauthorized - Authentication failed, invalid subject", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: banks:write", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "bank not found", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "invalid bank_guid value", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchBank"}}}, "required": true}}}, "/api/customers": {"post": {"summary": "Create Customer", "tags": ["Customers"], "operationId": "createCustomer", "description": "Creates a customer.\n\n## Customer Type\n\nCustomer resources are an abstraction for real world individuals and businesses on the Cybrid Platform and are used throughout the platform to perform high level operations, e.g., create a quote, execute a trade, etc..\n\nCustomers can have additional resources attached to them, e.g., identity records, accounts, etc.\n\nAt present, Customer's can be created with type `individual`.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the customer details in our private store |\n| unverified | The Platform has not yet verified the customer's identity |\n| verified | The Platform has verified the customer's identity |\n| rejected | The Platform was not able to successfully verify the customer's identity |\n\n\n\nRequired scope: **customers:execute**", "security": [{"BearerAuth": [], "oauth2": ["customers:execute"]}], "parameters": [], "responses": {"201": {"description": "customer created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Customer"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: type", "message_code": "missing_parameter"}}, "invalid_parameter_value": {"value": {"status": 400, "error_message": "Invalid value exception. Parameter: type supported values: [\"individual\"] ", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, invalid subject", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: customers:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostCustomer"}}}, "required": true}}, "get": {"summary": "Get customers list", "tags": ["Customers"], "operationId": "listCustomers", "description": "Retrieves a listing of customers.\n\nRequired scope: **customers:read**", "security": [{"BearerAuth": [], "oauth2": ["customers:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list customers for.", "schema": {"type": "string"}}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list customers for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of customers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, invalid subject,", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: customers:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/customers/{customer_guid}": {"get": {"summary": "Get Customer", "tags": ["Customers"], "operationId": "getCustomer", "description": "Retrieves a customer.\n\nRequired scope: **customers:read**", "security": [{"BearerAuth": [], "oauth2": ["customers:read"]}], "parameters": [{"name": "customer_guid", "in": "path", "required": true, "description": "Identifier for the customer.", "schema": {"type": "string"}}], "responses": {"200": {"description": "customer found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Customer"}}}}, "401": {"description": "Unauthorized - Authentication failed, invalid subject", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: customers:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "customer not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "customer not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/deposit_addresses": {"post": {"summary": "Create De<PERSON>sit Address", "tags": ["DepositAddresses"], "operationId": "createDepositAddress", "description": "Creates a deposit address.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the deposit address details in our private store |\n| created | The Platform has created the deposit address |\n\n\n\nRequired scope: **deposit_addresses:execute**", "security": [{"BearerAuth": [], "oauth2": ["deposit_addresses:execute"]}], "parameters": [], "responses": {"201": {"description": "Deposit Address created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositAddress"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: deposit_addresses:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostDepositAddress"}}}, "required": true}}, "get": {"summary": "List Deposit Addresses", "tags": ["DepositAddresses"], "operationId": "listDepositAddresses", "description": "Retrieves a list of deposit addresses.\n\nRequired scope: **deposit_addresses:read**", "security": [{"BearerAuth": [], "oauth2": ["deposit_addresses:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated guids to list deposit addresses for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list deposit addresses for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list deposit addresses for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of deposit addresses", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositAddressList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: deposit_addresses:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/deposit_addresses/{deposit_address_guid}": {"get": {"summary": "Get Deposit Address", "tags": ["DepositAddresses"], "operationId": "getDepositAddress", "description": "Retrieves a deposit address.\n\nRequired scope: **deposit_addresses:read**", "security": [{"BearerAuth": [], "oauth2": ["deposit_addresses:read"]}], "parameters": [{"name": "deposit_address_guid", "in": "path", "required": true, "description": "Identifier for the deposit address.", "schema": {"type": "string"}}], "responses": {"200": {"description": "deposit address found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepositAddress"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: deposit_addresses:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "deposit_address not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "deposit_address not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/external_bank_accounts": {"post": {"summary": "Create ExternalBankAccount", "tags": ["External Bank Accounts"], "operationId": "createExternalBankAccount", "description": "Create an ExternalBankAccount.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the external bank account details in our private store |\n| completed | The Platform has created the external bank account |\n| failed | The Platform was not able to successfully create the external bank account |\n| refresh_required | The Platform has created the external bank account, but needs to be refreshed |\n| deleting | The Platform is deleting the external bank account |\n| deleted | The Platform has deleted the external bank account |\n\n\n\nRequired scope: **external_bank_accounts:execute**", "security": [{"BearerAuth": [], "oauth2": ["external_bank_accounts:execute"]}], "parameters": [], "responses": {"201": {"description": "ExternalBankAccount created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalBankAccount"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: name, asset, account_kind", "message_code": "missing_parameter"}}, "invalid_value": {"value": {"status": 400, "error_message": "Invalid value. plaid_account_id must be at the most 128 characters long", "message_code": "invalid_value"}}, "invalid_parameter_value": {"value": {"status": 400, "error_message": "Invalid value exception. Parameter: account_kind supported values: [\"plaid\", \"plaid_processor_token\"] ", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or customer", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_bank_accounts:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unprocessable_request": {"value": {"status": 422, "error_message": "Unable to process request: InvalidCustomer - Customer not found", "message_code": "no_customer"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostExternalBankAccount"}}}, "required": true}}, "get": {"summary": "Get external bank accounts list", "tags": ["External Bank Accounts"], "operationId": "listExternalBankAccounts", "description": "Retrieves a listing of external bank accounts.\n\nRequired scope: **external_bank_accounts:read**", "security": [{"BearerAuth": [], "oauth2": ["external_bank_accounts:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated external_bank_account_guids to list external_bank_accounts for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list external_bank_accounts for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list external_bank_accounts for.", "schema": {"type": "string"}}, {"name": "state", "in": "query", "required": false, "description": "Comma separated states to list external_bank_accounts for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "Get list of external_bank_accounts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalBankAccountList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_bank_accounts:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/external_bank_accounts/{external_bank_account_guid}": {"delete": {"summary": "Delete External Bank Account", "tags": ["External Bank Accounts"], "operationId": "deleteExternalBankAccount", "description": "Deletes an external bank account.\n\nRequired scope: **external_bank_accounts:execute**", "security": [{"BearerAuth": [], "oauth2": ["external_bank_accounts:execute"]}], "parameters": [{"name": "external_bank_account_guid", "in": "path", "required": true, "description": "Identifier for the external bank account.", "schema": {"type": "string"}}], "responses": {"200": {"description": "External bank account deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalBankAccount"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_bank_accounts:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "ExternalBankAccount not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "ExternalBankAccount not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"summary": "Get External Bank Account", "tags": ["External Bank Accounts"], "operationId": "getExternalBankAccount", "description": "Retrieves an external bank account.\n\nRequired scope: **external_bank_accounts:read**", "security": [{"BearerAuth": [], "oauth2": ["external_bank_accounts:read"]}], "parameters": [{"name": "external_bank_account_guid", "in": "path", "required": true, "description": "Identifier for the external bank account.", "schema": {"type": "string"}}], "responses": {"200": {"description": "External bank account found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalBankAccount"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_bank_accounts:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "ExternalBankAccount not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "ExternalBankAccount not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "patch": {"summary": "<PERSON>ccount", "tags": ["External Bank Accounts"], "operationId": "patchExternalBankAccount", "description": "Patch an external bank account.\n\nRequired scope: **external_bank_accounts:write**", "security": [{"BearerAuth": [], "oauth2": ["external_bank_accounts:write"]}], "parameters": [{"name": "external_bank_account_guid", "in": "path", "required": true, "description": "Identifier for the external bank account.", "schema": {"type": "string"}}], "responses": {"200": {"description": "external bank account found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalBankAccount"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_bank_accounts:write", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "ExternalBankAccount not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "ExternalBankAccount not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchExternalBankAccount"}}}, "required": true}}}, "/api/external_wallets": {"post": {"summary": "Create ExternalWallet", "tags": ["External Wallets"], "operationId": "createExternalWallet", "description": "Create an ExternalWallet.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the external wallet details in our private store |\n| pending | The Platform is waiting for the external wallet to be created |\n| completed | The Platform has created the external wallet |\n| failed | The Platform was not able to successfully create the external wallet |\n| deleting | The Platform is deleting the external wallet |\n| deleted | The Platform has deleted the external wallet |\n\n\n\nExternal wallets can be added to the bank by leaving the customer_guid blank. External wallets added to the bank can be used by any customer of the bank.\n\nExternal wallets can also be added to a specific customer by providing the customer_guid. External wallets added to a customer can only be used by that customer.\n\nRequired scope: **external_wallets:execute**", "security": [{"BearerAuth": [], "oauth2": ["external_wallets:execute"]}], "parameters": [], "responses": {"201": {"description": "ExternalWallet created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalWallet"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: name, asset", "message_code": "missing_parameter"}}, "invalid_value": {"value": {"status": 400, "error_message": "Invalid value. customer_guid must  32 characters long", "message_code": "invalid_value"}}, "invalid_parameter_value": {"value": {"status": 400, "error_message": "Invalid value exception. Parameter: account_kind supported values: [\"fireblocks_external_wallet\", \"circle_wire_external_wallet\"] ", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or customer", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_wallets:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Data already exists", "content": {"application/json": {"examples": {"already_exists": {"value": {"status": 409, "error_message": "Data exists: External wallet already exists", "message_code": "data_exists"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unprocessable_request": {"value": {"status": 422, "error_message": "Unable to process request: InvalidCustomer - Customer not found", "message_code": "no_customer"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostExternalWallet"}}}, "required": true}}, "get": {"summary": "Get external wallets list", "tags": ["External Wallets"], "operationId": "listExternalWallets", "description": "Retrieves a listing of external wallets.\n\nRequired scope: **external_wallets:read**", "security": [{"BearerAuth": [], "oauth2": ["external_wallets:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated external_wallet_guids to list external_wallets for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list external_wallets for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list external_wallets for.", "schema": {"type": "string"}}, {"name": "state", "in": "query", "required": false, "description": "Comma separated states to list external_wallets for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "Get list of external_wallets", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalWalletList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_wallets:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/external_wallets/{external_wallet_guid}": {"delete": {"summary": "Delete External Wallet", "tags": ["External Wallets"], "operationId": "deleteExternalWallet", "description": "Deletes an external wallet.\n\nRequired scope: **external_wallets:execute**", "security": [{"BearerAuth": [], "oauth2": ["external_wallets:execute"]}], "parameters": [{"name": "external_wallet_guid", "in": "path", "required": true, "description": "Identifier for the external wallet.", "schema": {"type": "string"}}], "responses": {"200": {"description": "External wallet deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalWallet"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_wallets:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "ExternalWallet not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "ExternalWallet not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"summary": "Get External Wallet", "tags": ["External Wallets"], "operationId": "getExternalWallet", "description": "Retrieves an external_wallet.\n\nRequired scope: **external_wallets:read**", "security": [{"BearerAuth": [], "oauth2": ["external_wallets:read"]}], "parameters": [{"name": "external_wallet_guid", "in": "path", "required": true, "description": "Identifier for the external_wallet.", "schema": {"type": "string"}}], "responses": {"200": {"description": "External wallet found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalWallet"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: external_wallets:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "ExternalWallet not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "ExternalWallet not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/identity_verifications": {"post": {"summary": "Create Identity Verification", "tags": ["IdentityVerifications"], "operationId": "createIdentityVerification", "description": "Creates an Identity Verification.\n\n## Identity Verifications\n\nIdentity Verifications confirm an individual's identity with for the purpose of inclusion on the platform. This know-your-customer (KYC) process is a requirement for individuals to be able to transact. At present, we offer support for Cybrid performing the verification or working with partners to accept their KYC/AML process and have it attested to in our platform.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the identity verification details in our private store |\n| waiting | The Platform is waiting for the customer to complete the identity verification process |\n| expired | The identity verification process has expired |\n| completed | The identity verification process has been completed |\n\n## Outcome\n\n| State | Description |\n|-------|-------------|\n| passed | The customer has passed the identity verification process |\n| failed | The customer has failed the identity verification process |\n\n\n\nRequired scope: **customers:write**", "security": [{"BearerAuth": [], "oauth2": ["customers:write"]}], "parameters": [], "responses": {"201": {"description": "Identity Verification created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerification"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: customers:write", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostIdentityVerification"}}}, "required": true}}, "get": {"summary": "List Identity Verifications", "tags": ["IdentityVerifications"], "operationId": "listIdentityVerifications", "description": "Retrieves a list of identity verifications.\n\nRequired scope: **customers:read**", "security": [{"BearerAuth": [], "oauth2": ["customers:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated guids to list identity verifications for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list identity verifications for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list identity verifications for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of identity verifications", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: customers:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/identity_verifications/{identity_verification_guid}": {"get": {"summary": "Get Identity Verification", "tags": ["IdentityVerifications"], "operationId": "getIdentityVerification", "description": "Retrieves an identity verification.\n\nRequired scope: **customers:read**", "security": [{"BearerAuth": [], "oauth2": ["customers:read"]}], "parameters": [{"name": "identity_verification_guid", "in": "path", "required": true, "description": "Identifier for the identity verification.", "schema": {"type": "string"}}], "responses": {"200": {"description": "identity verification found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IdentityVerificationWithDetails"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: customers:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "identity_verification not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "identity_verification not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/accounts": {"post": {"summary": "Create Account", "tags": ["Accounts"], "operationId": "createAccount", "description": "Creates an account.\n\n## Account Type\n\nAn Account is tied to a specific cryptocurrency or fiat and is comprised of transactions and a current balance.\n\nAn account is required to allow a Customer to hold cryptocurrency or fiat on the Cybrid Platform.\n\nAt present, account's can be created as `trading` or `fiat ` accounts and are required before a Customer can generate quotes or execute a `trade` or `transfer`.\n\n## Asset\n\nThe asset is the specific cryptocurrency or fiat that the account holds, e.g., 'BTC' for Bitcoin or `USD` for US dollars. See the Symbols API for a complete list of cryptocurrencies and fiat supported. \n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the account details in our private store |\n| created | The Platform has created the account |\n\n\n\nRequired scope: **accounts:execute**", "security": [{"BearerAuth": [], "oauth2": ["accounts:execute"]}], "parameters": [], "responses": {"201": {"description": "account created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: type, customer_guid, asset, name", "message_code": "missing_parameter"}}, "invalid_parameter_value": {"value": {"status": 400, "error_message": "Invalid value exception. Parameter: type supported values: [\"trading\"] ", "message_code": "invalid_value"}}, "invalid_value": {"value": {"status": 400, "error_message": "name must be at the most 128 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: accounts:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostAccount"}}}, "required": true}}, "get": {"summary": "List Accounts", "tags": ["Accounts"], "operationId": "listAccounts", "description": "Retrieves a list of accounts.\n\nRequired scope: **accounts:read**", "security": [{"BearerAuth": [], "oauth2": ["accounts:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated account_guids to list accounts for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list accounts for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list accounts for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of accounts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: accounts:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/accounts/{account_guid}": {"get": {"summary": "Get Account", "tags": ["Accounts"], "operationId": "getAccount", "description": "Retrieves an account.\n\nRequired scope: **accounts:read**", "security": [{"BearerAuth": [], "oauth2": ["accounts:read"]}], "parameters": [{"name": "account_guid", "in": "path", "required": true, "description": "Identifier for the account.", "schema": {"type": "string"}}], "responses": {"200": {"description": "account found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: accounts:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "account not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "account not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/prices": {"get": {"summary": "Get Price", "tags": ["Prices"], "operationId": "listPrices", "description": "Retrieves a listing of symbol prices.\n\n## Symbols\n\nSymbol are pairs and are in the format asset-counter_asset, e.g., 'BTC-USD' for the Bitcoin/ USD pair. See the Symbols API for a complete list of cryptocurrencies supported.\n\n\n\nRequired scope: **prices:read**", "security": [{"BearerAuth": [], "oauth2": ["prices:read"]}], "parameters": [{"name": "symbol", "in": "query", "required": false, "description": "Comma separated symbols to list prices for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "The bank identifier to retrieve prices for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of price", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SymbolPriceResponse"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "comma separated symbols must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: prices:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/quotes": {"post": {"summary": "Create Quote", "tags": ["Quotes"], "operationId": "createQuote", "description": "Creates a quote.\n\nRequired scope: **quotes:execute**", "security": [{"BearerAuth": [], "oauth2": ["quotes:execute"]}], "parameters": [], "responses": {"201": {"description": "quote created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Quote"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: customer_guid, symbol, side", "message_code": "missing_parameter"}}, "invalid_value": {"value": {"status": 400, "error_message": "Invalid value. customer guids must be at the most 32 characters long", "message_code": "invalid_value"}}, "invalid_parameter": {"value": {"status": 400, "error_message": "Invalid parameter. receive_amount, deliver_amount are mutually exclusive. Atleast one of them should be present", "message_code": "invalid_parameter"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: quotes:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unprocessable_request": {"value": {"status": 422, "error_message": "Unable to process request: InvalidQuoteAmountException - quote amount is below the configured threshold", "message_code": "invalid_quote_amount"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"examples": {"internal_server_error": {"value": {"status": 500, "error_message": "Unable to process request: exchange has no fiat account", "message_code": "internal_server_error"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostQuote"}}}, "required": true}}, "get": {"summary": "Get quotes list", "tags": ["Quotes"], "operationId": "listQuotes", "description": "Retrieves a listing of quotes for all customers of a bank.\n\nRequired scope: **quotes:read**", "security": [{"BearerAuth": [], "oauth2": ["quotes:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated quote_guids to list quotes for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list quotes for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list quotes for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of quotes", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuoteList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: quotes:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/quotes/{quote_guid}": {"get": {"summary": "Get Quote", "tags": ["Quotes"], "operationId": "getQuote", "description": "Retrieves a quote.\n\nRequired scope: **quotes:read**", "security": [{"BearerAuth": [], "oauth2": ["quotes:read"]}], "parameters": [{"name": "quote_guid", "in": "path", "required": true, "description": "Identifier for the quote.", "schema": {"type": "string"}}], "responses": {"200": {"description": "quote found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Quote"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: quotes:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "quote not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "quote not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/rewards": {"post": {"summary": "Create Re<PERSON>", "tags": ["Rewards"], "operationId": "createRewards", "description": "Creates a reward.\n\nRequired scope: **rewards:execute**", "security": [{"BearerAuth": [], "oauth2": ["rewards:execute"]}], "parameters": [], "responses": {"201": {"description": "reward created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Reward"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: customer_guid, symbol", "message_code": "missing_parameter"}}, "invalid_value": {"value": {"status": 400, "error_message": "Invalid value. customer guids must be at the most 32 characters long", "message_code": "invalid_value"}}, "invalid_parameter": {"value": {"status": 400, "error_message": "Invalid parameter. receive_amount, deliver_amount are mutually exclusive. At least one of them should be present", "message_code": "invalid_parameter"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: rewards:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostReward"}}}, "required": true}}, "get": {"summary": "Get rewards list", "tags": ["Rewards"], "operationId": "listRewards", "description": "Retrieves a listing of rewards.\n\nRequired scope: **rewards:read**", "security": [{"BearerAuth": [], "oauth2": ["rewards:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated reward_guids to list rewards for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list rewards for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list rewards for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of rewards", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RewardList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: rewards:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/rewards/{reward_guid}": {"get": {"summary": "Get Re<PERSON>", "tags": ["Rewards"], "operationId": "getReward", "description": "Retrieves a reward.\n\nRequired scope: **rewards:read**", "security": [{"BearerAuth": [], "oauth2": ["rewards:read"]}], "parameters": [{"name": "reward_guid", "in": "path", "required": true, "description": "Identifier for the reward.", "schema": {"type": "string"}}], "responses": {"200": {"description": "reward found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Reward"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: rewards:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "reward not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "reward not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/symbols": {"get": {"summary": "Get Symbols list", "tags": ["Symbols"], "operationId": "listSymbols", "description": "Retrieves a listing of symbols.", "security": [{"BearerAuth": [], "oauth2": []}], "responses": {"200": {"description": "get list of symbols", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Symbols"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}, "invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or customer", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/trades": {"post": {"summary": "Create Trade", "tags": ["Trades"], "operationId": "createTrade", "description": "Creates a trade.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the trade details in our private store |\n| initiating | The Platform has begun to perform the trade |\n| pending | The Platform is executing the trade |\n| settling | The Platform is settling the trade |\n| completed | The Platform has successfully completed the trade |\n| failed | The Platform was not able to successfully complete the trade |\n\n\n\nRequired scope: **trades:execute**", "security": [{"BearerAuth": [], "oauth2": ["trades:execute"]}], "parameters": [], "responses": {"201": {"description": "Trade created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Trade"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: quote_guid", "message_code": "missing_parameter"}}, "invalid_value": {"value": {"status": 400, "error_message": "Invalid value. quote guid must be at the most 32 characters long", "message_code": "invalid_value"}}, "invalid_parameter": {"value": {"status": 400, "error_message": "Invalid parameter. expected_error not permitted in production", "message_code": "invalid_parameter"}}, "invalid_parameter_value": {"value": {"status": 400, "error_message": "Invalid value exception. Parameter: expected_error supported values: [\"invalid_quote_guid\", \"expired_quote\", \"non_sufficient_funds\"] ", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: trades:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Data already exists", "content": {"application/json": {"examples": {"already_exists": {"value": {"status": 409, "error_message": "Data exists: Trade for quote already exists", "message_code": "data_exists"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unprocessable_request": {"value": {"status": 422, "error_message": "Unable to process request: QuoteExpiredException - Quote has expired", "message_code": "quote_expired"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostTrade"}}}, "required": true}}, "get": {"summary": "Get trades list", "tags": ["Trades"], "operationId": "listTrades", "description": "Retrieves a listing of trades.\n\nRequired scope: **trades:read**", "security": [{"BearerAuth": [], "oauth2": ["trades:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated trade_guids to list trades for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list trades for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list trades for.", "schema": {"type": "string"}}, {"name": "account_guid", "in": "query", "required": false, "description": "Comma separated account_guids to list trades for.", "schema": {"type": "string"}}, {"name": "state", "in": "query", "required": false, "description": "Comma separated states to list trades for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of trades", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated account guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: trades:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/trades/{trade_guid}": {"get": {"summary": "Get Trade", "tags": ["Trades"], "operationId": "getTrade", "description": "Retrieves a trade.\n\nRequired scope: **trades:read**", "security": [{"BearerAuth": [], "oauth2": ["trades:read"]}], "parameters": [{"name": "trade_guid", "in": "path", "required": true, "description": "Identifier for the trade.", "schema": {"type": "string"}}], "responses": {"200": {"description": "trade found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Trade"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: trades:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "trade not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "trade not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/transfers": {"post": {"summary": "Create Transfer", "tags": ["Transfers"], "operationId": "createTransfer", "description": "Creates a transfer.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the trade details in our private store |\n| initiating | The Platform has begun to perform the transfer |\n| pending | The Platform is executing the transfer |\n| completed | The Platform has successfully completed the transfer |\n| failed | The Platform was not able to successfully complete the transfer |\n\n\n\nRequired scope: **transfers:execute**", "security": [{"BearerAuth": [], "oauth2": ["transfers:execute"]}], "parameters": [], "responses": {"201": {"description": "Transfer created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Transfer"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: quote_guid", "message_code": "missing_parameter"}}, "invalid_value": {"value": {"status": 400, "error_message": "Invalid value. quote guid must be at the most 32 characters long", "message_code": "invalid_value"}}, "invalid_parameter": {"value": {"status": 400, "error_message": "Invalid parameter. expected_error not permitted in production", "message_code": "invalid_parameter"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: transfers:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Data already exists", "content": {"application/json": {"examples": {"already_exists": {"value": {"status": 409, "error_message": "Data exists: Transfer for quote already exists", "message_code": "data_exists"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unprocessable_request": {"value": {"status": 422, "error_message": "Unable to process request: QuoteExpiredException - Quote has expired", "message_code": "quote_expired"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostTransfer"}}}, "required": true}}, "get": {"summary": "Get transfers list", "tags": ["Transfers"], "operationId": "listTransfers", "description": "Retrieves a listing of transfers.\n\nRequired scope: **transfers:read**", "security": [{"BearerAuth": [], "oauth2": ["transfers:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated trade_guids to list transfers for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list transfers for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list transfers for.", "schema": {"type": "string"}}, {"name": "account_guid", "in": "query", "required": false, "description": "Comma separated account_guids to list transfers for.", "schema": {"type": "string"}}, {"name": "state", "in": "query", "required": false, "description": "Comma separated states to list transfers for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of transfers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated account guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: transfers:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/transfers/{transfer_guid}": {"get": {"summary": "Get Transfer", "tags": ["Transfers"], "operationId": "getTransfer", "description": "Retrieves a transfer.\n\nRequired scope: **transfers:read**", "security": [{"BearerAuth": [], "oauth2": ["transfers:read"]}], "parameters": [{"name": "transfer_guid", "in": "path", "required": true, "description": "Identifier for the transfer.", "schema": {"type": "string"}}], "responses": {"200": {"description": "transfer found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Transfer"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: transfers:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "transfer not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "transfer not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/workflows": {"post": {"summary": "Create Workflow", "tags": ["Workflows"], "operationId": "createWorkflow", "description": "Creates a workflow.\n\n## State\n\n| State | Description |\n|-------|-------------|\n| storing | The Platform is storing the workflow details in our private store |\n| completed | The Platform has created the workflow |\n| failed | The workflow was not completed successfully |\n\n## Plaid\n\n| Param | Description |\n|-------|-------------|\n| redirect_uri | All URIs must be registered with Cybrid. For local testing use `http://localhost:4200/bank-account-connect` |\n\n\n\nRequired scope: **workflows:execute**", "security": [{"BearerAuth": [], "oauth2": ["workflows:execute"]}], "parameters": [], "responses": {"201": {"description": "Workflow created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Workflow"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"missing_parameter": {"value": {"status": 400, "error_message": "Missing parameter. Required: type", "message_code": "missing_parameter"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: workflows:execute", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Unable to process request", "content": {"application/json": {"examples": {"unprocessable_request": {"value": {"status": 422, "error_message": "Unable to process request: InvalidBankCountryCodeConfiguration - Invalid bank configuration: country codes", "message_code": "invalid_parameter"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostWorkflow"}}}, "required": true}}, "get": {"summary": "Get workflows list", "tags": ["Workflows"], "operationId": "listWorkflows", "description": "Retrieves a listing of workflows.\n\nRequired scope: **workflows:read**", "security": [{"BearerAuth": [], "oauth2": ["workflows:read"]}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPage"}, "description": "The page index to retrieve."}, {"name": "per_page", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ListRequestPerPage"}, "description": "The number of entities per page to return."}, {"name": "guid", "in": "query", "required": false, "description": "Comma separated workflow_guids to list workflows for.", "schema": {"type": "string"}}, {"name": "bank_guid", "in": "query", "required": false, "description": "Comma separated bank_guids to list workflows for.", "schema": {"type": "string"}}, {"name": "customer_guid", "in": "query", "required": false, "description": "Comma separated customer_guids to list workflows for.", "schema": {"type": "string"}}], "responses": {"200": {"description": "get list of workflows", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowsList"}}}}, "400": {"description": "Invalid requests", "content": {"application/json": {"examples": {"invalid_value": {"value": {"status": 400, "error_message": "Invalid value. comma separated bank guids must be at the most 4096 characters long", "message_code": "invalid_value"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: workflows:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/workflows/{workflow_guid}": {"get": {"summary": "Get Workflow", "tags": ["Workflows"], "operationId": "getWorkflow", "description": "Retrieves a workflow.\n\nRequired scope: **workflows:read**", "security": [{"BearerAuth": [], "oauth2": ["workflows:read"]}], "parameters": [{"name": "workflow_guid", "in": "path", "required": true, "description": "Identifier for the workflow.", "schema": {"type": "string"}}], "responses": {"200": {"description": "trade found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowWithDetails"}}}}, "401": {"description": "Unauthorized - Authentication failed, ", "content": {"application/json": {"examples": {"invalid_subject": {"value": {"status": 401, "error_message": "Invalid Subject type. Required: customer or bank or organization", "message_code": "invalid_subject"}}, "invalid_signature": {"value": {"status": 401, "error_message": "Signature verification raised", "message_code": "authentication_failed"}}, "invalid_audience": {"value": {"status": 401, "error_message": "Invalid audience", "message_code": "authentication_failed"}}, "invalid_issuer": {"value": {"status": 401, "error_message": "Invalid issuer", "message_code": "authentication_failed"}}, "expired_signature": {"value": {"status": 401, "error_message": "Signature has expired", "message_code": "authentication_failed"}}, "malformed_auth_header": {"value": {"status": 401, "error_message": "Authorization header is malformed.", "message_code": "authentication_malformed"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Invalid scope", "content": {"application/json": {"examples": {"invalid_scope": {"value": {"status": 403, "error_message": "Invalid scopes. Required: workflows:read", "message_code": "invalid_scopes"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "workflow not found", "content": {"application/json": {"examples": {"not_found": {"value": {"status": 404, "error_message": "workflow not found", "message_code": "not_found"}}}, "schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"clientCredentials": {"tokenUrl": "https://id.sandbox.cybrid.app/oauth/token", "scopes": {"banks:read": "banks read", "banks:write": "banks write", "accounts:read": "accounts read", "accounts:execute": "accounts execute", "customers:read": "customers read", "customers:write": "customers write", "customers:execute": "customers execute", "prices:read": "prices read", "quotes:execute": "quotes execute", "quotes:read": "quotes read", "trades:execute": "trades execute", "trades:read": "trades read", "transfers:execute": "transfers execute", "transfers:read": "transfers read", "rewards:execute": "rewards execute", "rewards:read": "rewards read", "external_bank_accounts:read": "external_bank_accounts read", "external_bank_accounts:write": "external_bank_accounts write", "external_bank_accounts:execute": "external_bank_accounts execute", "external_wallets:read": "external_wallets read", "external_wallets:execute": "external_wallets execute", "workflows:read": "workflows read", "workflows:execute": "workflows execute", "deposit_addresses:read": "deposit_addresses read", "deposit_addresses:execute": "deposit_addresses execute"}}}}, "BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Asset": {"type": "object", "properties": {"type": {"description": "The asset type.", "type": "string", "enum": ["fiat", "crypto"]}, "code": {"description": "The unique code for the asset.", "type": "string", "minLength": 1, "maxLength": 8}, "name": {"description": "The name of the asset.", "type": "string", "minLength": 1, "maxLength": 128}, "symbol": {"description": "The currency symbol for the asset.", "type": "string", "minLength": 1, "maxLength": 8}, "decimals": {"description": "The number of decimals for the default unit of the asset.", "type": "integer", "minimum": 0}}, "required": ["type", "code", "name", "symbol", "decimals"]}, "AssetList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of bank entities", "items": {"$ref": "#/components/schemas/Asset"}}}, "required": ["total", "page", "per_page", "objects"]}, "Bank": {"type": "object", "properties": {"guid": {"description": "Auto-generated unique identifier for the bank.", "type": "string", "minLength": 32, "maxLength": 32}, "organization_guid": {"description": "The organization's identifier.", "type": "string", "minLength": 32, "maxLength": 32}, "name": {"description": "The bank's name.", "type": "string", "minLength": 1, "maxLength": 128}, "type": {"description": "The bank's type.", "type": "string", "enum": ["sandbox", "production"]}, "supported_trading_symbols": {"description": "The bank's list of supported trading symbols.", "type": "array", "items": {"type": "string"}}, "supported_fiat_account_assets": {"description": "The bank's list of supported fiat symbols.", "type": "array", "items": {"type": "string"}}, "supported_country_codes": {"description": "The bank's list of supported country codes.", "type": "array", "items": {"type": "string"}}, "features": {"description": "The bank's enabled features.", "type": "array", "items": {"type": "string", "enum": ["attestation_identity_records", "kyc_identity_verifications", "backstopped_funding_source", "plaid_funding_source"]}}, "created_at": {"description": "ISO8601 datetime the bank was created at.", "type": "string", "format": "date-time"}}, "required": ["guid", "organization_guid", "name", "type", "features", "created_at"]}, "BankList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of bank entities", "items": {"$ref": "#/components/schemas/Bank"}}}, "required": ["total", "page", "per_page", "objects"]}, "Customer": {"type": "object", "properties": {"guid": {"description": "Auto-generated unique identifier for the customer.", "type": "string", "minLength": 32, "maxLength": 32}, "bank_guid": {"description": "Auto-generated unique identifier for the customer's bank.", "type": "string", "minLength": 32, "maxLength": 32}, "type": {"description": "The customer's type.", "type": "string", "enum": ["individual"]}, "created_at": {"description": "ISO8601 datetime the customer was created at.", "type": "string", "format": "date-time"}, "state": {"description": "The customer's state.", "type": "string", "enum": ["storing", "unverified", "verified", "rejected"]}}}, "CustomerList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/Customer"}}}, "required": ["total", "page", "per_page", "objects"]}, "IdentityVerification": {"type": "object", "properties": {"guid": {"description": "Auto-generated unique identifier for the identity verification.", "type": "string", "minLength": 32, "maxLength": 32}, "customer_guid": {"description": "The customer's identifier.", "type": "string", "minLength": 32, "maxLength": 32}, "type": {"description": "The type of identity verification.", "type": "string", "enum": ["kyc"]}, "method": {"description": "The identity verification method.", "type": "string", "enum": ["id_and_selfie", "attested"]}, "created_at": {"description": "ISO8601 datetime the customer was created at.", "type": "string", "format": "date-time"}, "state": {"description": "The state of the verification process.", "type": "string", "enum": ["storing", "waiting", "expired", "completed"]}, "outcome": {"description": "The outcome of the verification process.", "type": "string", "enum": ["passed", "failed"], "nullable": true}, "failure_codes": {"type": "array", "description": "The reason codes explaining the outcome.", "items": {"type": "string", "enum": ["requested_failure", "id_check_failure", "database_check_failure", "selfie_failure", "pep_check_failure", "watchlist_check_failure", "name_check_failure", "address_check_failure", "dob_check_failure", "id_number_check_failure"]}}}}, "IdentityVerificationWithDetails": {"allOf": [{"$ref": "#/components/schemas/IdentityVerification"}, {"type": "object", "properties": {"persona_inquiry_id": {"description": "The Persona identifier of the backing inquiry.", "type": "string", "nullable": true}, "persona_state": {"description": "The Persona state of the backing inquiry.", "type": "string", "enum": ["waiting", "pending", "reviewing", "processing", "expired", "completed", "unknown"], "nullable": true}}}]}, "IdentityVerificationList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/IdentityVerification"}}}, "required": ["total", "page", "per_page", "objects"]}, "DepositAddress": {"type": "object", "properties": {"guid": {"description": "Auto-generated unique identifier for the identity verification.", "type": "string", "minLength": 32, "maxLength": 32}, "bank_guid": {"description": "The address' bank identifier.", "type": "string", "minLength": 32, "maxLength": 32}, "customer_guid": {"description": "The address' customer identifier.", "type": "string", "minLength": 32, "maxLength": 32}, "account_guid": {"description": "The address' account identifier.", "type": "string", "minLength": 32, "maxLength": 32}, "created_at": {"description": "ISO8601 datetime the address was created at.", "type": "string", "format": "date-time"}, "asset": {"type": "string", "description": "The asset the transfer is related to, e.g., USD."}, "state": {"description": "The state of the address.", "type": "string", "enum": ["storing", "created"]}, "address": {"description": "The blockchain address.", "type": "string"}, "format": {"description": "The blockchain address format.", "type": "string", "enum": ["standard", "legacy"]}, "tag": {"description": "The blockchain address tag.", "type": "string"}}, "required": ["guid", "bank_guid", "customer_guid", "account_guid", "created_at", "asset", "state"]}, "DepositAddressList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/DepositAddress"}}}, "required": ["total", "page", "per_page", "objects"]}, "SymbolPrice": {"type": "object", "properties": {"symbol": {"type": "string", "minLength": 1, "maxLength": 16, "description": "The trade symbol the pricing is related to. Format is asset-counter_asset, e.g., BTC-USD."}, "buy_price": {"type": "integer", "description": "The purchase price (in base units) for the asset denominated in the counter asset currency.", "nullable": true}, "sell_price": {"type": "integer", "description": "The sale price (in base units) for the asset denominated in the counter asset currency.", "nullable": true}, "buy_price_last_updated_at": {"type": "string", "description": "ISO8601 datetime the purchase price was generated at.", "format": "date-time", "nullable": true}, "sell_price_last_updated_at": {"type": "string", "description": "ISO8601 datetime the sale price was generated at.", "format": "date-time", "nullable": true}}}, "SymbolPriceResponse": {"type": "array", "description": "Array of SymbolPrice entities", "items": {"$ref": "#/components/schemas/SymbolPrice"}}, "PostQuote": {"type": "object", "properties": {"product_type": {"type": "string", "description": "The type of product the quote is for.", "enum": ["trading", "funding", "book_transfer", "crypto_transfer"], "default": "trading"}, "customer_guid": {"type": "string", "minLength": 32, "maxLength": 32, "description": "The unique identifier for the customer."}, "asset": {"type": "string", "minLength": 1, "maxLength": 8, "description": "The asset code the quote was requested for. Populated for funding, book transfer and crypto transfer quotes."}, "symbol": {"type": "string", "minLength": 1, "maxLength": 16, "description": "Symbol the quote is being requested for. Format is \"asset-counter_asset\" in uppercase. See the Symbols API for a complete list of cryptocurrencies supported. Populated for trade quotes."}, "side": {"type": "string", "description": "The direction for trade quotes: either 'buy' or 'sell'. The direction for funding quotes: either 'deposit' or 'withdrawal'. The direction for book transfer quotes: either 'deposit' or 'withdrawal'. The direction for crypto transfer quotes: 'withdrawal'. ", "enum": ["buy", "sell", "deposit", "withdrawal"]}, "receive_amount": {"type": "integer", "description": "The amount to be received in base units of the currency: currency is \"asset\" for buy and \"counter_asset\" for sell for trade quotes."}, "deliver_amount": {"type": "integer", "description": "The amount to be delivered in base units of the currency: currency is \"counter_asset\" for buy and \"asset\" for sell for trade quotes."}}, "required": ["side"]}, "Quote": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the quote.", "minLength": 32, "maxLength": 32}, "product_type": {"type": "string", "description": "The type of product the quote is for.", "enum": ["trading", "funding", "book_transfer", "crypto_transfer"]}, "customer_guid": {"type": "string", "description": "The unique identifier for the customer.", "minLength": 32, "maxLength": 32}, "symbol": {"type": "string", "description": "Symbol the quote was requested for. Format is \"asset-counter_asset\" in uppercase. Populated for trade quotes.", "minLength": 1, "maxLength": 16, "nullable": true}, "side": {"type": "string", "description": "The direction of the quote: either 'buy' or 'sell' for trade quotes.", "enum": ["buy", "sell", "deposit", "withdrawal"]}, "receive_amount": {"type": "integer", "description": "The amount to be received in base units of the currency: currency is \"asset\" for buy and \"counter_asset\" for sell for trade quotes."}, "deliver_amount": {"type": "integer", "description": "The amount to be delivered in base units of the currency: currency is \"counter_asset\" for buy and \"asset\" for sell for trade quotes."}, "fee": {"type": "integer", "description": "The fee associated with the trade. Denominated in \"counter_asset\" base units for trade quotes."}, "issued_at": {"type": "string", "description": "ISO8601 datetime the quote was created at.", "format": "date-time"}, "expires_at": {"type": "string", "description": "ISO8601 datetime the quote is expiring at. Populated for trading quotes.", "format": "date-time", "nullable": true}, "asset": {"type": "string", "minLength": 1, "maxLength": 8, "description": "The asset code the quote was requested for. Populated for book transfer and funding quotes.", "nullable": true}, "network_fee": {"type": "integer", "description": "The network fee in base units of network_fee_asset. Only present on `crypto_transfer` quotes.", "nullable": true}, "network_fee_asset": {"type": "string", "minLength": 1, "maxLength": 8, "description": "The asset code of the network fee.", "nullable": true}}}, "QuoteList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/Quote"}}}, "required": ["total", "page", "per_page", "objects"]}, "Symbols": {"type": "array", "description": "The list of symbols supported for trading.", "items": {"type": "string"}}, "Workflow": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the workflow.", "minLength": 32, "maxLength": 32}, "customer_guid": {"type": "string", "description": "The associated customer's identifier.", "minLength": 32, "maxLength": 32}, "type": {"type": "string", "description": "The type of workflow.", "enum": ["plaid"]}, "state": {"description": "The state of the workflow.", "type": "string", "enum": ["storing", "completed", "failed"]}, "failure_code": {"description": "The failure code for failed workflows.", "type": "string", "nullable": true}, "created_at": {"type": "string", "description": "ISO8601 datetime the bank was created at.", "format": "date-time"}}}, "WorkflowWithDetails": {"allOf": [{"$ref": "#/components/schemas/Workflow"}, {"type": "object", "properties": {"plaid_link_token": {"type": "string", "description": "The Plaid link token generated by the workflow.", "nullable": true}}}]}, "Transfer": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the trade.", "minLength": 32, "maxLength": 32}, "transfer_type": {"type": "string", "description": "The type of transfer.", "enum": ["savings", "funding", "book", "crypto", "internal_bank_rebalance", "internal_wallet_rebalance"]}, "customer_guid": {"type": "string", "description": "The associated customer's identifier.", "minLength": 32, "maxLength": 32}, "quote_guid": {"type": "string", "description": "The associated quote's identifier.", "minLength": 32, "maxLength": 32}, "external_bank_account_guid": {"type": "string", "description": "The associated external bank account's identifier.", "minLength": 32, "maxLength": 32, "nullable": true}, "asset": {"type": "string", "description": "The asset the transfer is related to, e.g., USD."}, "side": {"type": "string", "description": "The direction of the quote: 'deposit' or 'withdrawal'.", "enum": ["deposit", "withdrawal"]}, "state": {"type": "string", "description": "The trade's state", "enum": ["storing", "initiating", "pending", "completed", "failed"]}, "amount": {"type": "integer", "description": "The amount being transferred."}, "fee": {"type": "integer", "description": "The fee associated with the trade."}, "estimated_network_fee": {"type": "integer", "description": "The estimated network fee in base units of network_fee_asset. Only present on `crypto` transfers.", "nullable": true}, "network_fee": {"type": "integer", "description": "The actual network fee in base units of network_fee_asset. Only present on `crypto` transfers that have successfully completed.", "nullable": true}, "network_fee_asset": {"type": "string", "minLength": 1, "maxLength": 8, "description": "The asset code of the network fee. Only present on `crypto` transfers that have successfully completed.", "nullable": true}, "network_fee_liability_amount": {"type": "integer", "description": "The equivalent fiat network fee in base units of network_fee_liability_amount_asset. Only present on `crypto` transfers that have successfully completed.", "nullable": true}, "network_fee_liability_amount_asset": {"type": "string", "description": "The fiat asset the network_fee_liability_amount is denominated in. Only present on `crypto` transfers that have successfully completed.", "nullable": true}, "created_at": {"type": "string", "description": "ISO8601 datetime the bank was created at.", "format": "date-time"}}}, "Trade": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the trade.", "minLength": 32, "maxLength": 32}, "customer_guid": {"type": "string", "description": "The associated customer's identifier.", "nullable": true}, "quote_guid": {"type": "string", "description": "The associated quote's identifier.", "minLength": 32, "maxLength": 32}, "symbol": {"type": "string", "description": "The trade symbol the pricing is related to. Format is asset-counter_asset, e.g., BTC-USD."}, "side": {"type": "string", "description": "The direction of the quote: either 'buy' or 'sell'.", "enum": ["buy", "sell"]}, "state": {"type": "string", "description": "The trade's state", "enum": ["storing", "pending", "cancelled", "completed", "settling", "failed"]}, "receive_amount": {"type": "integer", "description": "The amount to be received in base units of the currency: currency is \"asset\" for buy and \"counter_asset\" for sell."}, "deliver_amount": {"type": "integer", "description": "The amount to be delivered in base units of the currency: currency is \"counter_asset\" for buy and \"asset\" for sell."}, "fee": {"type": "integer", "description": "The fee associated with the trade. Denominated in \"counter_asset\" base units"}, "created_at": {"type": "string", "description": "ISO8601 datetime the bank was created at.", "format": "date-time"}}}, "ExternalWalletList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of external wallet entities", "items": {"$ref": "#/components/schemas/ExternalWallet"}}}, "required": ["total", "page", "per_page", "objects"]}, "ExternalBankAccountList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of external bank account entities", "items": {"$ref": "#/components/schemas/ExternalBankAccount"}}}, "required": ["total", "page", "per_page", "objects"]}, "TransferList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of trade entities", "items": {"$ref": "#/components/schemas/Transfer"}}}, "required": ["total", "page", "per_page", "objects"]}, "TradeList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of trade entities", "items": {"$ref": "#/components/schemas/Trade"}}}, "required": ["total", "page", "per_page", "objects"]}, "WorkflowsList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of workflow entities", "items": {"$ref": "#/components/schemas/Workflow"}}}, "required": ["total", "page", "per_page", "objects"]}, "Account": {"type": "object", "properties": {"type": {"description": "The account type.", "type": "string", "enum": ["backstopped", "trading", "fee", "fiat", "gas"]}, "guid": {"description": "Auto-generated unique identifier for the account.", "type": "string", "minLength": 32, "maxLength": 32}, "created_at": {"type": "string", "description": "ISO8601 datetime the account was created at.", "format": "date-time"}, "asset": {"type": "string", "description": "The asset code.", "minLength": 1, "maxLength": 8}, "name": {"description": "The name of the account.", "type": "string", "minLength": 1, "maxLength": 128}, "bank_guid": {"type": "string", "description": "The bank identifier associated with the account.", "minLength": 32, "maxLength": 32}, "customer_guid": {"type": "string", "description": "The customer identifier associated with the account.", "minLength": 32, "maxLength": 32}, "platform_balance": {"type": "integer", "description": "The amount of funds that are in the account, in base units of the asset."}, "platform_available": {"type": "integer", "description": "The amount of funds that are in the account, in base units of the asset, that are available for use on the platform."}, "state": {"description": "The account's state.", "type": "string", "enum": ["storing", "created"]}}}, "AccountList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of account entities", "items": {"$ref": "#/components/schemas/Account"}}}, "required": ["total", "page", "per_page", "objects"]}, "ListRequestCursor": {"description": "The cursor used to retrieve the page.", "type": "string", "nullable": true}, "ListRequestPage": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "ListRequestPerPage": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 1, "maximum": 100}, "ExternalBankAccount": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the account.", "minLength": 32, "maxLength": 32}, "name": {"description": "The name of the account.", "type": "string", "minLength": 1, "maxLength": 128}, "asset": {"type": "string", "description": "The asset code.", "minLength": 1, "maxLength": 8}, "account_kind": {"type": "string", "description": "The type of account.", "enum": ["plaid", "plaid_processor_token"]}, "environment": {"type": "string", "description": "The environment that the external bank account is operating in.", "enum": ["sandbox", "production"]}, "bank_guid": {"type": "string", "description": "The bank identifier.", "minLength": 32, "maxLength": 32, "nullable": true}, "customer_guid": {"type": "string", "description": "The customer identifier.", "minLength": 32, "maxLength": 32, "nullable": true}, "created_at": {"type": "string", "description": "ISO8601 datetime the exchange was created at.", "format": "date-time"}, "plaid_institution_id": {"type": "string", "description": "The Plaid institution ID for the account.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_account_mask": {"type": "string", "description": "The account number mask for the account.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_account_name": {"type": "string", "description": "The name for the account.", "minLength": 1, "maxLength": 128, "nullable": true}, "state": {"description": "The state of the external bank account.", "type": "string", "enum": ["storing", "completed", "failed", "refresh_required", "deleting", "deleted"]}, "failure_code": {"description": "The failure code for failed transfers.", "type": "string", "nullable": true}}}, "ExternalWallet": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the wallet.", "minLength": 32, "maxLength": 32}, "name": {"description": "The name of the wallet.", "type": "string", "minLength": 1, "maxLength": 128}, "asset": {"type": "string", "description": "The asset code.", "minLength": 1, "maxLength": 8}, "environment": {"type": "string", "description": "The environment that the wallet is configured for.", "enum": ["sandbox", "production"]}, "bank_guid": {"type": "string", "description": "The bank identifier.", "minLength": 32, "maxLength": 32}, "customer_guid": {"type": "string", "description": "The customer identifier.", "minLength": 32, "maxLength": 32}, "address": {"description": "The blockchain wallet address for the wallet.", "maxLength": 256, "type": "string"}, "tag": {"description": "The blockchain tag to use when transferring crypto to the wallet.", "maxLength": 256, "type": "string", "nullable": true}, "created_at": {"type": "string", "description": "ISO8601 datetime the wallet was created at.", "format": "date-time"}, "state": {"type": "string", "description": "The state of an external wallet", "enum": ["storing", "pending", "failed", "completed", "deleting", "deleted"]}, "failure_code": {"type": "string", "description": "The failure code of an external wallet (if any)", "nullable": true}}}, "PostBank": {"type": "object", "properties": {"name": {"description": "The bank's name.", "minLength": 1, "maxLength": 128, "type": "string"}, "type": {"description": "The bank's type. At present, only **sandbox** is supported.", "type": "string", "enum": ["sandbox"]}, "supported_trading_symbols": {"description": "The bank's list of supported trading symbols.", "type": "array", "items": {"type": "string"}}, "features": {"description": "The bank's enabled features. At present, both **attestation_identity_records** and **backstopped_funding_source** must be set.", "type": "array", "items": {"type": "string", "enum": ["attestation_identity_records", "kyc_identity_verifications", "backstopped_funding_source", "plaid_funding_source"]}}, "supported_fiat_account_assets": {"description": "The bank's list of supported fiat assets.", "type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 1}, "supported_country_codes": {"description": "The bank's list of supported country codes.", "type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 1}}, "required": ["name", "type", "supported_trading_symbols", "supported_fiat_account_assets", "features"]}, "PatchBank": {"type": "object", "properties": {"name": {"description": "The bank's name.", "minLength": 1, "maxLength": 128, "type": "string"}, "supported_trading_symbols": {"description": "The bank's list of supported trading symbols.", "type": "array", "items": {"type": "string"}}}}, "PostCustomer": {"type": "object", "properties": {"type": {"description": "The customer's type.", "type": "string", "enum": ["individual"]}, "name": {"type": "object", "properties": {"first": {"description": "The customer's first name.", "type": "string"}, "middle": {"description": "The customer's middle name.", "type": "string", "nullable": true}, "last": {"description": "The customer's last name.", "type": "string"}}, "required": ["first", "last"]}, "address": {"type": "object", "properties": {"street": {"description": "The first line of the address.", "type": "string"}, "street2": {"description": "The optional second line of the address.", "type": "string", "nullable": true}, "city": {"description": "The city of the address.", "type": "string"}, "subdivision": {"description": "The provide/state/region of the address; not used by all countries.", "type": "string", "nullable": true}, "postal_code": {"description": "The postal/post/zip code of the address; not used by all countries.", "type": "string", "nullable": true}, "country_code": {"description": "The ISO 3166 country 2-Alpha country code of the address.", "type": "string"}}, "required": ["street", "city", "country_code"]}, "date_of_birth": {"type": "string", "description": "The customer's date of birth.", "format": "date"}, "phone_number": {"type": "string", "description": "The customer's phone number.", "nullable": true}, "email_address": {"type": "string", "description": "The customer's phone number.", "nullable": true}, "identification_numbers": {"type": "array", "description": "The customer's identification numbers.", "items": {"$ref": "#/components/schemas/PostIdentificationNumber"}, "nullable": true}}, "required": ["type"]}, "PostIdentityVerification": {"type": "object", "properties": {"type": {"description": "The type of identity verification.", "type": "string", "enum": ["kyc"]}, "method": {"description": "The identity verification method.", "type": "string", "enum": ["id_and_selfie", "attested"]}, "customer_guid": {"description": "The customer's identifier.", "type": "string", "minLength": 32, "maxLength": 32, "nullable": true}, "country_code": {"description": "The ISO 3166 country 2-Alpha country the customer is being verified in; required when method is set to 'id_and_selfie'. If not present, will default to the Bank's configured country code.", "type": "string", "minLength": 2, "maxLength": 2, "nullable": true}, "name": {"type": "object", "description": "The customer's name; required when method is set to 'attested'.", "properties": {"first": {"description": "The customer's first name.", "type": "string"}, "middle": {"description": "The customer's middle name.", "type": "string", "nullable": true}, "last": {"description": "The customer's last name.", "type": "string"}}, "required": ["first", "last"], "nullable": true}, "address": {"type": "object", "description": "The customer's address; required when method is set to 'attested'.", "properties": {"street": {"description": "The first line of the address.", "type": "string"}, "street2": {"description": "The optional second line of the address.", "type": "string", "nullable": true}, "city": {"description": "The city of the address.", "type": "string"}, "subdivision": {"description": "The provide/state/region of the address; not used by all countries.", "type": "string", "nullable": true}, "postal_code": {"description": "The postal/post/zip code of the address; not used by all countries.", "type": "string", "nullable": true}, "country_code": {"description": "The ISO 3166 country 2-Alpha country code of the address.", "type": "string", "minLength": 2, "maxLength": 2}}, "required": ["street", "city", "country_code"], "nullable": true}, "date_of_birth": {"type": "string", "description": "The customer's date of birth; required when method is set to 'attested'.", "format": "date", "nullable": true}, "identification_numbers": {"type": "array", "description": "The customer's identification numbers; required when method is set to 'attested'.", "items": {"$ref": "#/components/schemas/PostIdentificationNumber"}, "nullable": true}, "expected_behaviours": {"description": "The optional expected behaviour to simulate.", "type": "array", "items": {"type": "string", "enum": ["passed_immediately", "failed_immediately"]}}}, "required": ["type", "method"]}, "PostIdentificationNumber": {"type": "object", "properties": {"type": {"type": "string", "description": "The source of the identification number.", "enum": ["drivers_license", "passport", "passport_card", "residence_card", "social_security_number", "tax_identification_number"]}, "issuing_country_code": {"description": "The ISO 3166 country 2-Alpha country code of the identification number issuer.", "type": "string"}, "identification_number": {"description": "The identification number.", "type": "string"}}, "required": ["type", "issuing_country_code", "identification_number"]}, "PostDepositAddress": {"type": "object", "properties": {"account_guid": {"description": "The trading account guid.", "type": "string", "minLength": 32, "maxLength": 32}}, "required": ["account_guid"]}, "PostAccount": {"type": "object", "properties": {"type": {"description": "The account type.", "type": "string", "enum": ["trading", "fiat"]}, "customer_guid": {"type": "string", "minLength": 32, "maxLength": 32, "description": "The customer identifier associated with the account."}, "asset": {"type": "string", "minLength": 1, "maxLength": 8, "description": "The asset code."}, "name": {"description": "The name of the account.", "minLength": 1, "maxLength": 128, "type": "string"}}, "required": ["name", "type", "asset"]}, "PostWorkflow": {"type": "object", "properties": {"type": {"description": "The workflow type.", "type": "string", "enum": ["plaid"]}, "kind": {"description": "The Plaid workflow kind.", "type": "string", "enum": ["link_token_create", "link_token_update"]}, "customer_guid": {"type": "string", "minLength": 32, "maxLength": 32, "description": "The customer identifier associated with the workflow."}, "external_bank_account_guid": {"type": "string", "minLength": 32, "maxLength": 32, "description": "The external bank account identifier associated with the workflow."}, "language": {"description": "The language to initialize Plaid link.", "type": "string", "enum": ["en", "fr", "es", "nl", "de"]}, "link_customization_name": {"description": "The customization name for Plaid link.", "type": "string", "minLength": 1, "maxLength": 128}, "redirect_uri": {"description": "The redirect URI for Plaid link.", "type": "string", "minLength": 1, "maxLength": 128, "nullable": true}, "android_package_name": {"description": "The Android package name for Plaid link.", "type": "string", "minLength": 1, "maxLength": 128, "nullable": true}}, "required": ["type"]}, "PostTransfer": {"type": "object", "properties": {"quote_guid": {"description": "The associated quote's identifier.", "minLength": 32, "maxLength": 32, "type": "string"}, "transfer_type": {"type": "string", "description": "The type of transfer.", "enum": ["funding", "book", "crypto"]}, "external_wallet_guid": {"description": "The customer's external wallet's identifier.", "minLength": 32, "maxLength": 32, "type": "string"}, "external_bank_account_guid": {"description": "The customer's 'plaid' or 'plaid_processor_token' external bank account's identifier.", "minLength": 32, "maxLength": 32, "type": "string"}, "expected_error": {"description": "The optional expected error to simulate transfer failure.", "type": "string", "enum": ["pending", "in_progress", "completed", "failed", "cancelled", "manual_intervention", "reversed"]}}, "required": ["quote_guid", "transfer_type"]}, "PostTrade": {"type": "object", "properties": {"quote_guid": {"description": "The associated quote's identifier.", "minLength": 32, "maxLength": 32, "type": "string"}, "expected_error": {"description": "The optional expected error to simulate trade failure.", "type": "string", "enum": ["invalid_quote_guid", "expired_quote", "non_sufficient_funds"]}}, "required": ["quote_guid"]}, "PatchExternalBankAccount": {"type": "object", "properties": {"state": {"description": "The state of the external bank account.", "type": "string", "enum": ["completed", "refresh_required"]}}, "required": ["state"]}, "PostExternalBankAccount": {"type": "object", "properties": {"name": {"description": "The name of the account.", "type": "string", "minLength": 1, "maxLength": 128}, "account_kind": {"description": "The account type", "type": "string", "enum": ["plaid", "plaid_processor_token"]}, "customer_guid": {"type": "string", "description": "The customer identifier.", "minLength": 32, "maxLength": 32, "nullable": true}, "asset": {"type": "string", "description": "The asset code. If not set will try and default to the Bank's configured fiat asset.", "minLength": 1, "maxLength": 8, "nullable": true}, "plaid_public_token": {"type": "string", "description": "The public token for the account. Required for 'plaid' accounts.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_account_id": {"type": "string", "description": "The account identifier in plaid. Required for 'plaid' accounts.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_processor_token": {"type": "string", "description": "The Plaid processor token used to access the account. Required for 'plaid_processor_token' accounts.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_institution_id": {"type": "string", "description": "Plaid's institution ID for the account's institution. Required for 'plaid_processor_token' accounts.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_account_mask": {"type": "string", "description": "The account mask for the account. Required for 'plaid_processor_token' accounts.", "minLength": 1, "maxLength": 128, "nullable": true}, "plaid_account_name": {"type": "string", "description": "The name of the account. Required for 'plaid_processor_token' accounts.", "minLength": 1, "maxLength": 128, "nullable": true}}, "required": ["name", "account_kind", "asset"]}, "PostExternalWallet": {"type": "object", "properties": {"name": {"description": "The name of the account.", "type": "string", "minLength": 1, "maxLength": 128}, "customer_guid": {"type": "string", "description": "The customer identifier.", "minLength": 32, "maxLength": 32, "nullable": true}, "asset": {"type": "string", "description": "The asset code.", "minLength": 1, "maxLength": 8}, "address": {"description": "The blockchain wallet address for the wallet.", "maxLength": 256, "type": "string"}, "tag": {"description": "The blockchain tag to use when transferring crypto to the wallet.", "maxLength": 256, "type": "string", "nullable": true}}, "required": ["name", "asset", "address"]}, "ErrorResponse": {"type": "object", "properties": {"status": {"description": "Status code for Http Request", "type": "integer"}, "error_message": {"description": "Error message", "type": "string"}, "message_code": {"description": "Message code for Error", "type": "string"}}, "required": ["status", "error_message", "message_code"]}, "PostReward": {"type": "object", "properties": {"customer_guid": {"type": "string", "minLength": 32, "maxLength": 32, "description": "The unique identifier for the customer."}, "symbol": {"type": "string", "minLength": 1, "maxLength": 16, "description": "Symbol the quote is being requested for. Format is \"asset-counter_asset\" in uppercase. See the Symbols API for a complete list of cryptocurrencies supported."}, "receive_amount": {"type": "integer", "description": "The amount to be received in base units of the currency: currency is \"asset\" for buy and \"counter_asset\" for sell."}, "deliver_amount": {"type": "integer", "description": "The amount to be delivered in base units of the currency: currency is \"counter_asset\" for buy and \"asset\" for sell."}}, "required": ["customer_guid", "symbol"]}, "Reward": {"type": "object", "properties": {"guid": {"type": "string", "description": "Auto-generated unique identifier for the reward.", "minLength": 32, "maxLength": 32}, "customer_guid": {"type": "string", "description": "The associated customer's identifier.", "minLength": 32, "maxLength": 32}, "quote_guid": {"type": "string", "description": "The associated quote's identifier.", "minLength": 32, "maxLength": 32}, "trade_guid": {"type": "string", "description": "The associated trade's identifier.", "minLength": 32, "maxLength": 32}, "symbol": {"type": "string", "description": "The trade symbol the pricing is related to. Format is asset-counter_asset, e.g., BTC-USD."}, "state": {"type": "string", "description": "The trade's state", "enum": ["storing", "pending", "cancelled", "completed", "settling", "failed"]}, "receive_amount": {"type": "integer", "description": "The amount to be received in base units of the currency: currency is \"asset\" for buy and \"counter_asset\" for sell."}, "deliver_amount": {"type": "integer", "description": "The amount to be delivered in base units of the currency: currency is \"counter_asset\" for buy and \"asset\" for sell."}, "fee": {"type": "integer", "description": "The fee associated with the trade. Denominated in \"counter_asset\" base units"}, "created_at": {"type": "string", "description": "ISO8601 datetime the bank was created at.", "format": "date-time"}}}, "RewardList": {"type": "object", "properties": {"total": {"description": "The total number of records available.", "type": "integer", "minimum": 0}, "page": {"description": "The page index to retrieve.", "type": "integer", "minimum": 0}, "per_page": {"description": "The number of entities per page to return.", "type": "integer", "minimum": 0}, "objects": {"type": "array", "description": "Array of reward entities", "items": {"$ref": "#/components/schemas/Reward"}}}, "required": ["total", "page", "per_page", "objects"]}}}, "servers": [{"url": "https://bank.sandbox.cybrid.app", "variables": {"defaultHost": {"default": "https://bank.sandbox.cybrid.app"}}}]}